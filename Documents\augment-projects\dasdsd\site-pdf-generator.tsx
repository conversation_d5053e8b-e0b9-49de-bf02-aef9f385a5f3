import React, { useState } from 'react';
import { Globe, FileText, Download, Settings, Loader2, AlertCircle } from 'lucide-react';

interface Heading {
  level: string;
  text: string;
}

interface ListItem {
  type: string;
  items: string[];
}

interface PageContent {
  title: string;
  headings: Heading[];
  paragraphs: string[];
  lists: ListItem[];
}

interface ProcessedPage {
  url: string;
  content: PageContent;
}

interface Results {
  pages: number;
  content: string;
  baseUrl: string;
}

interface Settings {
  maxDepth: number;
  maxPages: number;
  includeImages: boolean;
  showSettings: boolean;
}

const SitePDFGenerator = () => {
  const [url, setUrl] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [results, setResults] = useState<Results | null>(null);
  const [error, setError] = useState<string>('');
  const [settings, setSettings] = useState<Settings>({
    maxDepth: 2,
    maxPages: 10,
    includeImages: false,
    showSettings: false
  });

  const cleanText = (text: string | null): string => {
    if (!text) return '';
    return text
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim();
  };

  const extractTextFromHTML = (html: string): PageContent => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // Remove scripts, styles, nav, footer, ads
    const removeSelectors = ['script', 'style', 'nav', 'footer', '.ad', '.advertisement', '.menu', '.sidebar'];
    removeSelectors.forEach(selector => {
      const elements = doc.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });

    // Extract main content
    const content: PageContent = {
      title: doc.title || 'Sem título',
      headings: [],
      paragraphs: [],
      lists: []
    };

    // Get headings
    const headings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(h => {
      if (h.textContent && h.textContent.trim()) {
        content.headings.push({
          level: h.tagName,
          text: cleanText(h.textContent)
        });
      }
    });

    // Get paragraphs
    const paragraphs = doc.querySelectorAll('p, article p, main p, .content p');
    paragraphs.forEach(p => {
      const text = cleanText(p.textContent);
      if (text.length > 20) { // Filter out very short paragraphs
        content.paragraphs.push(text);
      }
    });

    // Get lists
    const lists = doc.querySelectorAll('ul, ol');
    lists.forEach(list => {
      const items = Array.from(list.querySelectorAll('li')).map(li => cleanText(li.textContent));
      if (items.length > 0) {
        content.lists.push({
          type: list.tagName.toLowerCase(),
          items
        });
      }
    });

    return content;
  };

  const fetchPageContent = async (pageUrl: string): Promise<PageContent | null> => {
    try {
      // In a real implementation, you would use a CORS proxy or backend service
      // For demo purposes, we'll simulate the content extraction
      const response = await fetch(`https://api.allorigins.win/raw?url=${encodeURIComponent(pageUrl)}`);
      const html = await response.text();
      return extractTextFromHTML(html);
    } catch (err) {
      console.error('Error fetching page:', err);
      return null;
    }
  };

  const findSublinks = (html: string, baseUrl: string): string[] => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const links = Array.from(doc.querySelectorAll('a[href]'));

    const sublinks = new Set<string>();

    links.forEach(link => {
      const href = link.getAttribute('href');
      if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
        let fullUrl: string;
        try {
          fullUrl = new URL(href, baseUrl).toString();
          const baseUrlObj = new URL(baseUrl);
          const linkUrlObj = new URL(fullUrl);

          // Only include links from the same domain
          if (linkUrlObj.hostname === baseUrlObj.hostname) {
            sublinks.add(fullUrl);
          }
        } catch (e) {
          // Invalid URL, skip
        }
      }
    });

    return Array.from(sublinks);
  };

  const generatePDF = async () => {
    if (!url.trim()) {
      setError('Por favor, insira uma URL válida');
      return;
    }

    setIsProcessing(true);
    setError('');
    setProgress(0);
    setResults(null);

    try {
      const baseUrl = url.startsWith('http') ? url : `https://${url}`;
      const processedPages: ProcessedPage[] = [];
      const processedUrls = new Set<string>();

      setProgress(10);

      // Process main page
      const mainContent = await fetchPageContent(baseUrl);
      if (mainContent) {
        processedPages.push({
          url: baseUrl,
          content: mainContent
        });
        processedUrls.add(baseUrl);
      }

      setProgress(30);

      // Get sublinks from main page
      if (settings.maxDepth > 1) {
        try {
          const mainResponse = await fetch(`https://api.allorigins.win/raw?url=${encodeURIComponent(baseUrl)}`);
          const mainHtml = await mainResponse.text();
          const sublinks = findSublinks(mainHtml, baseUrl);
          
          // Limit number of sublinks to process
          const linksToProcess = sublinks.slice(0, settings.maxPages - 1);
          
          for (let i = 0; i < linksToProcess.length; i++) {
            const sublink = linksToProcess[i];
            if (!processedUrls.has(sublink)) {
              const sublinkContent = await fetchPageContent(sublink);
              if (sublinkContent) {
                processedPages.push({
                  url: sublink,
                  content: sublinkContent
                });
                processedUrls.add(sublink);
              }
              setProgress(30 + (i + 1) * (60 / linksToProcess.length));
            }
          }
        } catch (e) {
          console.error('Error processing sublinks:', e);
        }
      }

      setProgress(90);

      // Generate PDF content
      const pdfContent = generatePDFContent(processedPages, baseUrl);
      
      setProgress(100);
      setResults({
        pages: processedPages.length,
        content: pdfContent,
        baseUrl
      });

    } catch (err) {
      setError('Erro ao processar o site. Verifique a URL e tente novamente.');
      console.error('Processing error:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  const generatePDFContent = (pages: ProcessedPage[], baseUrl: string): string => {
    const domain = new URL(baseUrl).hostname;
    const date = new Date().toLocaleDateString('pt-BR');

    let content = `# Relatório do Site: ${domain}\n\n`;
    content += `**Data de extração:** ${date}\n`;
    content += `**URL base:** ${baseUrl}\n`;
    content += `**Páginas processadas:** ${pages.length}\n\n`;

    content += `## Índice\n\n`;
    pages.forEach((page, index) => {
      content += `${index + 1}. ${page.content.title}\n`;
    });
    content += `\n---\n\n`;

    pages.forEach((page, index) => {
      content += `## ${index + 1}. ${page.content.title}\n\n`;
      content += `**URL:** ${page.url}\n\n`;

      if (page.content.headings.length > 0) {
        content += `### Principais Seções:\n\n`;
        page.content.headings.forEach(heading => {
          const level = '#'.repeat(Math.min(parseInt(heading.level.charAt(1)) + 2, 6));
          content += `${level} ${heading.text}\n\n`;
        });
      }

      if (page.content.paragraphs.length > 0) {
        content += `### Conteúdo:\n\n`;
        page.content.paragraphs.forEach(paragraph => {
          content += `${paragraph}\n\n`;
        });
      }

      if (page.content.lists.length > 0) {
        content += `### Listas:\n\n`;
        page.content.lists.forEach(list => {
          list.items.forEach(item => {
            content += `• ${item}\n`;
          });
          content += `\n`;
        });
      }

      content += `---\n\n`;
    });

    return content;
  };

  const downloadPDF = () => {
    if (!results) return;
    
    const blob = new Blob([results.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `site-report-${new URL(results.baseUrl).hostname}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-2">
          <Globe className="text-blue-600" />
          Gerador de PDF de Sites
        </h1>
        <p className="text-gray-600">Extraia conteúdo relevante de sites e sublinks para gerar relatórios em PDF</p>
      </div>

      <div className="bg-gray-50 rounded-lg p-6 mb-6">
        <div className="flex gap-4 mb-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">URL do Site</label>
            <input
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://exemplo.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isProcessing}
            />
          </div>
          <div className="flex items-end">
            <button
              onClick={() => setSettings(prev => ({ ...prev, showSettings: !prev.showSettings }))}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
              disabled={isProcessing}
            >
              <Settings size={20} />
            </button>
          </div>
        </div>

        {settings.showSettings && (
          <div className="bg-white rounded-md p-4 mb-4 border">
            <h3 className="font-medium mb-3">Configurações</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm text-gray-600 mb-1">Profundidade máxima</label>
                <select
                  value={settings.maxDepth}
                  onChange={(e) => setSettings(prev => ({ ...prev, maxDepth: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value={1}>Apenas página principal</option>
                  <option value={2}>Página + sublinks</option>
                  <option value={3}>Até 3 níveis</option>
                </select>
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">Máximo de páginas</label>
                <select
                  value={settings.maxPages}
                  onChange={(e) => setSettings(prev => ({ ...prev, maxPages: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value={5}>5 páginas</option>
                  <option value={10}>10 páginas</option>
                  <option value={20}>20 páginas</option>
                  <option value={50}>50 páginas</option>
                </select>
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">Incluir imagens</label>
                <input
                  type="checkbox"
                  checked={settings.includeImages}
                  onChange={(e) => setSettings(prev => ({ ...prev, includeImages: e.target.checked }))}
                  className="mt-2"
                />
              </div>
            </div>
          </div>
        )}

        <button
          onClick={generatePDF}
          disabled={isProcessing || !url.trim()}
          className="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
        >
          {isProcessing ? (
            <>
              <Loader2 className="animate-spin" size={20} />
              Processando...
            </>
          ) : (
            <>
              <FileText size={20} />
              Gerar Relatório
            </>
          )}
        </button>

        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md flex items-center gap-2">
            <AlertCircle size={16} />
            {error}
          </div>
        )}

        {isProcessing && (
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Progresso</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {results && (
        <div className="bg-green-50 rounded-lg p-6 border border-green-200">
          <h2 className="text-xl font-semibold text-green-800 mb-4">Relatório Gerado com Sucesso!</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{results.pages}</div>
              <div className="text-sm text-gray-600">Páginas processadas</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(results.content.length / 1000)}K
              </div>
              <div className="text-sm text-gray-600">Caracteres extraídos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {results.content.split('\n').filter(line => line.trim()).length}
              </div>
              <div className="text-sm text-gray-600">Linhas de conteúdo</div>
            </div>
          </div>
          
          <div className="bg-white rounded-md p-4 max-h-64 overflow-y-auto mb-4 border">
            <h3 className="font-medium mb-2">Preview do Conteúdo:</h3>
            <pre className="text-sm text-gray-700 whitespace-pre-wrap">{results.content.substring(0, 1000)}...</pre>
          </div>
          
          <button
            onClick={downloadPDF}
            className="w-full bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
          >
            <Download size={20} />
            Baixar Relatório (.txt)
          </button>
          
          <p className="text-sm text-gray-600 mt-2 text-center">
            O arquivo será salvo como um documento de texto estruturado que pode ser convertido para PDF
          </p>
        </div>
      )}
    </div>
  );
};

export default SitePDFGenerator;